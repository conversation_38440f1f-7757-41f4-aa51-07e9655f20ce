import csv
import datetime
import platform
import socket
from typing import Dict, List

import psutil


def getComputerName() -> str:
    """
    Get the computer name.
    
    Returns:
        Computer name as string
    """
    return platform.node()


def getIpAddress() -> str:
    """
    Get the primary IP address of the computer.
    
    Returns:
        IP address as string
    """
    try:
        hostName = socket.gethostname()
        ipAddress = socket.gethostbyname(hostName)
        return ipAddress
    except Exception:
        return "Unknown"


def getMacAddress() -> str:
    """
    Get the MAC address of the primary network interface.
    
    Returns:
        MAC address as string
    """
    try:
        interfaces = psutil.net_if_addrs()
        
        for interfaceName, interfaceAddresses in interfaces.items():
            for address in interfaceAddresses:
                if address.family == psutil.AF_LINK:
                    return address.address
        return "Unknown"
    except Exception:
        return "Unknown"


def getProcessorModel() -> str:
    """
    Get the processor model information.
    
    Returns:
        Processor model as string
    """
    try:
        return platform.processor()
    except Exception:
        return "Unknown"


def getOperatingSystem() -> str:
    """
    Get the operating system information.
    
    Returns:
        Operating system details as string
    """
    try:
        system = platform.system()
        release = platform.release()
        return f"{system} {release}"
    except Exception:
        return "Unknown"


def getSystemTime() -> str:
    """
    Get the current system time in required format.
    
    Returns:
        System time as formatted string
    """
    try:
        now = datetime.datetime.now()
        return now.strftime("%d//%m//%Y %H:%M:%S")
    except Exception:
        return "Unknown"


def getInternetSpeed() -> str:
    """
    Test internet connection speed.
    
    Returns:
        Internet speed in Mb/s as string
    """
    try:
        import speedtest
        
        st = speedtest.Speedtest()
        st.get_best_server()
        downloadSpeed = st.download()
        speedMbps = downloadSpeed / 1_000_000
        return f"{speedMbps:.0f}Mb/s"
    except ImportError:
        return "speedtest-cli not installed"
    except Exception:
        return "Test failed"


def getActivePorts() -> str:
    """
    Get list of active network ports.
    
    Returns:
        Semicolon-delimited list of active ports as string
    """
    try:
        activePorts = []
        connections = psutil.net_connections()
        
        for connection in connections:
            if connection.laddr and connection.status == 'LISTEN':
                port = connection.laddr.port
                if port not in activePorts:
                    activePorts.append(port)
        
        activePorts.sort()
        return ";".join(map(str, activePorts[:10]))
    except Exception:
        return "Unknown"


def collectFingerprint() -> Dict[str, str]:
    """
    Collect all system information to create a computer fingerprint.
    
    Returns:
        Dictionary containing all system information
    """
    fingerprint = {
        'Computer Name': getComputerName(),
        'IP Address': getIpAddress(),
        'MAC Address': getMacAddress(),
        'Processor Model': getProcessorModel(),
        'Operating System': getOperatingSystem(),
        'System Time': getSystemTime(),
        'Internet Speed': getInternetSpeed(),
        'Active Ports': getActivePorts()
    }
    
    return fingerprint


def loadExistingData(csvFileName: str) -> List[Dict[str, str]]:
    """
    Load existing data from CSV file.
    
    Args:
        csvFileName: Name of the CSV file
        
    Returns:
        List of dictionaries containing existing data
    """
    try:
        with open(csvFileName, 'r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            return list(reader)
    except FileNotFoundError:
        return []


def checkForDifferences(existingData: List[Dict[str, str]], fingerprint: Dict[str, str]) -> bool:
    """
    Check if current fingerprint differs from existing data.
    
    Args:
        existingData: List of existing computer data
        fingerprint: Current computer fingerprint
        
    Returns:
        True if differences found, False otherwise
    """
    computerName = fingerprint['Computer Name']
    macAddress = fingerprint['MAC Address']
    
    for row in existingData:
        if (row.get('Computer Name') == computerName or 
            row.get('MAC Address') == macAddress):
            
            for key in fingerprint:
                if key in row and row[key] != fingerprint[key]:
                    print(f"WARNING: {key} has changed from '{row[key]}' to '{fingerprint[key]}'")
                    return True
    
    return False


def saveToCsv(fingerprint: Dict[str, str], csvFileName: str = "computer_info.csv") -> None:
    """
    Save or update computer info in CSV file.
    
    Args:
        fingerprint: Dictionary containing system information
        csvFileName: Name of the CSV file to store computer fingerprints
    """
    fieldNames = [
        'Computer Name',
        'IP Address',
        'MAC Address', 
        'Processor Model',
        'Operating System',
        'System Time',
        'Internet Speed',
        'Active Ports'
    ]
    
    try:
        existingData = loadExistingData(csvFileName)
        
        # Check for differences and display warnings
        checkForDifferences(existingData, fingerprint)
        
        computerName = fingerprint['Computer Name']
        macAddress = fingerprint['MAC Address']
        
        updated = False
        for i, row in enumerate(existingData):
            if (row.get('Computer Name') == computerName or 
                row.get('MAC Address') == macAddress):
                existingData[i] = fingerprint
                updated = True
                break
        
        if not updated:
            existingData.append(fingerprint)
        
        with open(csvFileName, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=fieldNames)
            writer.writeheader()
            writer.writerows(existingData)
            
        print(f"Computer fingerprint saved to {csvFileName}")
        
    except Exception as e:
        print(f"Error saving to CSV: {e}")


def main():
    """
    Main function to run fingerprinting.
    """
    
    print("Collecting computer fingerprint information...")
    fingerprint = collectFingerprint()
    
    saveToCsv(fingerprint)
    
    print("\nCompleted successfully!")


if __name__ == "__main__":
    main()

